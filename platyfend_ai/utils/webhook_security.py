"""
Webhook security utilities for validating GitHub webhook signatures.
"""

import hashlib
import hmac
import logging
from typing import Optional

from fastapi import HTT<PERSON><PERSON>xception, Request, status

from platyfend_ai.config.settings import settings

logger = logging.getLogger(__name__)


class WebhookSecurityError(Exception):
    """Exception raised for webhook security validation errors."""
    pass


class GitHubWebhookValidator:
    """Validates GitHub webhook signatures using HMAC-SHA256."""
    
    def __init__(self, webhook_secret: Optional[str] = None):
        """
        Initialize the webhook validator.
        
        Args:
            webhook_secret: GitHub webhook secret for signature validation
        """
        self.webhook_secret = webhook_secret or settings.github_webhook_secret
        
        if not self.webhook_secret:
            logger.warning(
                "No GitHub webhook secret configured - webhook signature validation will be skipped"
            )
    
    def validate_signature(self, payload: bytes, signature_header: str) -> bool:
        """
        Validate GitHub webhook signature using HMAC-SHA256.
        
        Args:
            payload: Raw request body as bytes
            signature_header: Value of X-Hub-Signature-256 header
            
        Returns:
            True if signature is valid, False otherwise
            
        Raises:
            WebhookSecurityError: If validation fails due to configuration issues
        """
        if not self.webhook_secret:
            logger.warning("Webhook secret not configured - skipping signature validation")
            return True  # Fail open if not configured
        
        if not signature_header:
            raise WebhookSecurityError("Missing X-Hub-Signature-256 header")
        
        # GitHub sends signature as "sha256=<hex_digest>"
        if not signature_header.startswith("sha256="):
            raise WebhookSecurityError("Invalid signature format - must start with 'sha256='")
        
        # Extract the hex digest
        expected_signature = signature_header[7:]  # Remove "sha256=" prefix
        
        # Calculate HMAC-SHA256 signature
        calculated_signature = hmac.new(
            self.webhook_secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        # Use constant-time comparison to prevent timing attacks
        is_valid = hmac.compare_digest(expected_signature, calculated_signature)
        
        if not is_valid:
            logger.warning(
                f"Webhook signature validation failed. "
                f"Expected: {expected_signature[:8]}..., "
                f"Calculated: {calculated_signature[:8]}..."
            )
        
        return is_valid
    
    async def validate_request(self, request: Request) -> bool:
        """
        Validate a FastAPI request's webhook signature.
        
        Args:
            request: FastAPI Request object
            
        Returns:
            True if signature is valid
            
        Raises:
            HTTPException: If signature validation fails
        """
        try:
            # Get the signature header
            signature_header = request.headers.get("X-Hub-Signature-256")
            
            # Read the raw body
            body = await request.body()
            
            # Validate the signature
            is_valid = self.validate_signature(body, signature_header)
            
            if not is_valid:
                logger.error("Webhook signature validation failed")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid webhook signature"
                )
            
            logger.debug("Webhook signature validation successful")
            return True
            
        except WebhookSecurityError as e:
            logger.error(f"Webhook security error: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Webhook security validation failed: {e}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during webhook validation: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal error during webhook validation"
            )


# Global webhook validator instance
webhook_validator = GitHubWebhookValidator()


async def validate_github_webhook(request: Request) -> bool:
    """
    FastAPI dependency for validating GitHub webhook signatures.
    
    Args:
        request: FastAPI Request object
        
    Returns:
        True if validation passes
        
    Raises:
        HTTPException: If validation fails
    """
    return await webhook_validator.validate_request(request)


def verify_webhook_signature(payload: bytes, signature: str, secret: str) -> bool:
    """
    Standalone function to verify webhook signature.
    
    Args:
        payload: Raw webhook payload as bytes
        signature: X-Hub-Signature-256 header value
        secret: Webhook secret
        
    Returns:
        True if signature is valid, False otherwise
    """
    validator = GitHubWebhookValidator(secret)
    try:
        return validator.validate_signature(payload, signature)
    except WebhookSecurityError:
        return False
