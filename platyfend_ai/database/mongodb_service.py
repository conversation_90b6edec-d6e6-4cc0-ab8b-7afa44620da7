"""MongoDB service for managing organization and installation data with connection pooling."""

import asyncio
from contextlib import asynccontextmanager
from typing import Dict, List, Optional

from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import PyMongoError, ServerSelectionTimeoutError

from platyfend_ai.config.settings import settings
from platyfend_ai.utils.logging_config import get_logger

logger = get_logger(__name__)


class MongoDBConnectionPool:
    """MongoDB connection pool manager for efficient connection reuse."""

    _instance: Optional["MongoDBConnectionPool"] = None
    _lock = asyncio.Lock()

    def __init__(self):
        """Initialize connection pool."""
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        self.mongodb_uri = settings.mongodb_uri
        self.database_name = settings.mongodb_database
        self._connected = False

    @classmethod
    async def get_instance(cls) -> "MongoDBConnectionPool":
        """Get singleton instance of connection pool."""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
                    await cls._instance.connect()
        return cls._instance

    async def connect(self) -> None:
        """Establish connection pool to MongoDB."""
        if self._connected:
            return

        if not self.mongodb_uri:
            raise ValueError("MongoDB URI is required for connection")

        try:
            # Configure connection pool settings
            self.client = AsyncIOMotorClient(
                self.mongodb_uri,
                # Connection pool settings
                maxPoolSize=20,  # Maximum number of connections in the pool
                minPoolSize=5,  # Minimum number of connections to maintain
                maxIdleTimeMS=30000,  # Close connections after 30 seconds of inactivity
                waitQueueTimeoutMS=5000,  # Wait up to 5 seconds for a connection
                serverSelectionTimeoutMS=settings.mongodb_timeout * 1000,
                connectTimeoutMS=10000,  # 10 second connection timeout
                socketTimeoutMS=30000,  # 30 second socket timeout
                # Health check settings
                heartbeatFrequencyMS=10000,  # Check server health every 10 seconds
                # Retry settings
                retryWrites=True,
                retryReads=True,
            )

            self.database = self.client[self.database_name]

            # Test the connection
            await self.client.admin.command("ping")
            self._connected = True

            logger.info(
                "MongoDB connection pool established",
                database=self.database_name,
                max_pool_size=20,
                min_pool_size=5,
            )

        except PyMongoError as e:
            logger.error("Failed to establish MongoDB connection pool", error=str(e))
            raise

    async def disconnect(self) -> None:
        """Close connection pool."""
        if self.client and self._connected:
            self.client.close()
            self._connected = False
            logger.info("MongoDB connection pool closed")

    async def get_database(self) -> AsyncIOMotorDatabase:
        """Get database instance from connection pool."""
        if not self._connected:
            await self.connect()

        if self.database is None:
            raise RuntimeError("Failed to establish database connection")

        return self.database

    async def health_check(self) -> bool:
        """Check if the connection pool is healthy."""
        try:
            if not self._connected or self.client is None:
                return False

            # Ping the database with a short timeout
            await asyncio.wait_for(self.client.admin.command("ping"), timeout=5.0)
            return True
        except Exception as e:
            logger.warning("MongoDB health check failed", error=str(e))
            return False


class MongoDBService:
    """MongoDB service using connection pooling for efficient database operations."""

    def __init__(self):
        """Initialize MongoDB service with connection pooling."""
        self.pool: Optional[MongoDBConnectionPool] = None

    async def get_database(self) -> AsyncIOMotorDatabase:
        """Get database instance from connection pool."""
        if self.pool is None:
            self.pool = await MongoDBConnectionPool.get_instance()

        return await self.pool.get_database()

    async def get_collection(self, collection_name: str):
        """Get a collection from the database using connection pool."""
        database = await self.get_database()
        return database[collection_name]

    async def health_check(self) -> bool:
        """Check if the MongoDB connection is healthy."""
        if self.pool is None:
            self.pool = await MongoDBConnectionPool.get_instance()

        return await self.pool.health_check()

    @asynccontextmanager
    async def transaction(self):
        """Context manager for MongoDB transactions."""
        database = await self.get_database()

        async with await database.client.start_session() as session:
            async with session.start_transaction():
                try:
                    yield session
                except Exception:
                    await session.abort_transaction()
                    raise
                else:
                    await session.commit_transaction()


class OrganizationService:
    """Service for managing GitHub organization and installation data."""

    def __init__(self, mongodb_service: Optional[MongoDBService] = None):
        """
        Initialize organization service.

        Args:
            mongodb_service: MongoDB service instance
        """
        self.mongodb_service = mongodb_service or MongoDBService()
        self.collection_name = "organizations"

    async def get_installation_id_by_repo(self, repository_name: str) -> Optional[int]:
        """
        Get GitHub App installation ID for a repository.

        Args:
            repository_name: Repository name in format "owner/repo"

        Returns:
            Installation ID if found, None otherwise
        """
        try:
            collection = await self.mongodb_service.get_collection(self.collection_name)

            # Query for organization by owner name
            query = {"repos.full_name": repository_name}
            organization = await collection.find_one(query)

            if organization and "installation_id" in organization:
                installation_id = organization["installation_id"]
                logger.info(
                    f"Found installation ID {installation_id} for repository {repository_name}"
                )
                return int(installation_id)
            else:
                logger.warning(
                    f"No installation ID found for repository {repository_name}"
                )
                return None

        except PyMongoError as e:
            logger.error(
                f"MongoDB error while querying installation ID for {repository_name}: {e}"
            )
            return None
        except Exception as e:
            logger.error(
                f"Unexpected error while querying installation ID for {repository_name}: {e}"
            )
            return None

    async def get_organization_by_installation_id(
        self, installation_id: int
    ) -> Optional[Dict]:
        """
        Get organization data by installation ID.

        Args:
            installation_id: GitHub App installation ID

        Returns:
            Organization document if found, None otherwise
        """
        try:
            collection = await self.mongodb_service.get_collection(self.collection_name)

            query = {"installation_id": installation_id}
            organization = await collection.find_one(query)

            if organization:
                logger.info(f"Found organization for installation ID {installation_id}")
                return organization
            else:
                logger.warning(
                    f"No organization found for installation ID {installation_id}"
                )
                return None

        except PyMongoError as e:
            logger.error(
                f"MongoDB error while querying organization for installation ID {installation_id}: {e}"
            )
            return None
        except Exception as e:
            logger.error(
                f"Unexpected error while querying organization for installation ID {installation_id}: {e}"
            )
            return None

    async def list_organizations(self) -> List[Dict]:
        """
        List all organizations.

        Returns:
            List of organization documents
        """
        try:
            collection = await self.mongodb_service.get_collection(self.collection_name)

            organizations = []
            async for org in collection.find({}):
                organizations.append(org)

            logger.info(f"Retrieved {len(organizations)} organizations")
            return organizations

        except PyMongoError as e:
            logger.error(f"MongoDB error while listing organizations: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error while listing organizations: {e}")
            return []

    async def upsert_organization(
        self, owner: str, installation_id: int, **kwargs
    ) -> bool:
        """
        Insert or update organization data.

        Args:
            owner: GitHub organization/user owner name
            installation_id: GitHub App installation ID
            **kwargs: Additional organization data

        Returns:
            True if successful, False otherwise
        """
        try:
            collection = await self.mongodb_service.get_collection(self.collection_name)

            organization_data = {
                "owner": owner,
                "installation_id": installation_id,
                **kwargs,
            }

            # Upsert based on owner
            result = await collection.update_one(
                {"owner": owner}, {"$set": organization_data}, upsert=True
            )

            if result.upserted_id or result.modified_count > 0:
                logger.info(
                    f"Successfully upserted organization {owner} with installation ID {installation_id}"
                )
                return True
            else:
                logger.warning(f"No changes made for organization {owner}")
                return False

        except PyMongoError as e:
            logger.error(f"MongoDB error while upserting organization {owner}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error while upserting organization {owner}: {e}")
            return False
