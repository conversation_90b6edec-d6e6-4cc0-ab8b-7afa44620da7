import logging
from typing import Any, Dict, List, Optional

import httpx
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from platyfend_ai.config.settings import settings
from platyfend_ai.database import OrganizationService
from platyfend_ai.models.analysis import ReviewComment
from platyfend_ai.services.github_app_service import (
    GitHubAppTokenError,
    GitHubTokenService,
)
from platyfend_ai.utils.error_handling import (
    ErrorCategory,
    ErrorSeverity,
    GitHubAPIError,
)
from platyfend_ai.utils.error_handling import RateLimitError as PlatyfendRateLimitError

logger = logging.getLogger(__name__)


class GitHubCommentError(GitHubAPIError):
    """Exception raised when GitHub comment operations fail"""

    def __init__(self, message: str, **kwargs):
        kwargs.setdefault("component", "github_comment_service")
        super().__init__(message, **kwargs)


class GitHubRateLimitError(PlatyfendRateLimitError):
    """Exception raised when GitHub API rate limit is exceeded"""

    def __init__(self, message: str, **kwargs):
        kwargs.setdefault("component", "github_comment_service")
        super().__init__(message, **kwargs)


class GitHubCommentService:
    """Service for posting review comments to GitHub PRs"""

    def __init__(
        self, github_token: Optional[str] = None, use_dynamic_tokens: bool = True
    ):
        """
        Initialize the GitHub comment service.

        Args:
            github_token: GitHub personal access token for authentication (fallback)
            use_dynamic_tokens: Whether to use dynamic token generation based on repository
        """
        self.github_token = github_token or settings.github_token
        self.use_dynamic_tokens = use_dynamic_tokens

        # Initialize services for dynamic token generation
        if self.use_dynamic_tokens:
            self.organization_service = OrganizationService()
            self.token_service = GitHubTokenService()

        self.base_headers = {
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": f"{settings.app_name}/{settings.version}",
        }

        # Only set static authorization if not using dynamic tokens
        if self.github_token and not self.use_dynamic_tokens:
            self.base_headers["Authorization"] = f"token {self.github_token}"

        # HTTP client configuration
        self.timeout = httpx.Timeout(connect=10.0, read=30.0, write=30.0, pool=5.0)

        logger.info(
            f"GitHub comment service initialized with {'dynamic' if self.use_dynamic_tokens else 'static'} token authentication"
        )

    async def _get_token_for_repository(self, repository: str) -> Optional[str]:
        """
        Get the appropriate GitHub token for a repository.

        Args:
            repository: Repository name in format "owner/repo"

        Returns:
            GitHub token string or None if not available
        """
        if not self.use_dynamic_tokens:
            return self.github_token

        try:
            # Get installation ID for the repository
            installation_id = (
                await self.organization_service.get_installation_id_by_repo(repository)
            )

            if installation_id:
                # Get installation token from Next.js service
                token = await self.token_service.get_installation_token(installation_id)
                logger.info(
                    f"Retrieved dynamic token for repository {repository} (installation {installation_id})"
                )
                return token
            else:
                logger.warning(
                    f"No installation ID found for repository {repository}, falling back to static token"
                )
                return self.github_token

        except GitHubAppTokenError as e:
            logger.error(
                f"Failed to get dynamic token for repository {repository}: {e}"
            )
            logger.info("Falling back to static token")
            return self.github_token
        except Exception as e:
            logger.error(
                f"Unexpected error getting token for repository {repository}: {e}"
            )
            logger.info("Falling back to static token")
            return self.github_token

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, GitHubRateLimitError)),
    )
    async def _make_request(
        self,
        method: str,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> httpx.Response:
        """
        Make an HTTP request to GitHub API with retry logic.

        Args:
            method: HTTP method (GET, POST, etc.)
            url: URL to request
            data: Request body data
            headers: Additional headers

        Returns:
            HTTP response

        Raises:
            GitHubCommentError: If the request fails after retries
            GitHubRateLimitError: If rate limit is exceeded
        """
        request_headers = self.base_headers.copy()
        if headers:
            request_headers.update(headers)

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:

                if method.upper() == "GET":
                    response = await client.get(url, headers=request_headers)
                elif method.upper() == "POST":
                    response = await client.post(
                        url, json=data, headers=request_headers
                    )
                elif method.upper() == "PATCH":
                    response = await client.patch(
                        url, json=data, headers=request_headers
                    )
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=request_headers)
                else:
                    raise GitHubCommentError(f"Unsupported HTTP method: {method}")

                # Handle rate limiting
                if response.status_code == 403:
                    if "rate limit" in response.text.lower():
                        reset_time = response.headers.get("X-RateLimit-Reset")
                        logger.warning(
                            f"GitHub API rate limit exceeded. Reset time: {reset_time}"
                        )
                        raise GitHubRateLimitError(
                            f"GitHub API rate limit exceeded. Reset time: {reset_time}"
                        )
                    elif "abuse" in response.text.lower():
                        logger.warning("GitHub API abuse detection triggered")
                        raise GitHubRateLimitError(
                            "GitHub API abuse detection triggered"
                        )

                # Handle authentication errors
                if response.status_code == 401:
                    raise GitHubCommentError(
                        "GitHub authentication failed. Check token permissions."
                    )
                elif response.status_code == 404:
                    raise GitHubCommentError(
                        "GitHub resource not found. Check repository and PR permissions."
                    )
                elif response.status_code >= 400:
                    error_detail = (
                        response.text[:500] if response.text else "Unknown error"
                    )
                    raise GitHubCommentError(
                        f"GitHub API error {response.status_code}: {error_detail}"
                    )

                response.raise_for_status()
                return response

            except httpx.RequestError as e:
                logger.error(f"Request error for {url}: {e}")
                raise GitHubCommentError(f"Network error: {e}")

    async def post_review_comments(
        self,
        repository: str,
        pr_number: int,
        comments: List[ReviewComment],
        commit_sha: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Post review comments to a GitHub PR.

        Args:
            repository: Repository name in format "owner/repo"
            pr_number: Pull request number
            comments: List of review comments to post
            commit_sha: Optional specific commit SHA to review

        Returns:
            Dictionary with posting results
        """
        if not comments:
            logger.info("No comments to post")
            return {"posted_comments": 0, "errors": []}

        logger.info(f"Posting {len(comments)} comments to {repository}#{pr_number}")

        # Get PR information if commit_sha not provided
        if not commit_sha:
            pr_info = await self._get_pr_info(repository, pr_number)
            commit_sha = pr_info.get("head", {}).get("sha")

            if not commit_sha:
                raise GitHubCommentError("Could not determine commit SHA for PR")

        # Separate inline comments from general comments
        inline_comments = [c for c in comments if c.file_path and c.line]
        general_comments = [c for c in comments if not (c.file_path and c.line)]

        results = {
            "posted_comments": 0,
            "errors": [],
            "inline_comments": 0,
            "general_comments": 0,
        }

        # Post inline comments as a review
        if inline_comments:
            try:
                review_result = await self._post_review_with_comments(
                    repository, pr_number, inline_comments, commit_sha
                )
                results["posted_comments"] += len(inline_comments)
                results["inline_comments"] = len(inline_comments)
                results["review_id"] = review_result.get("id")
            except Exception as e:
                error_msg = f"Failed to post inline comments: {e}"
                logger.error(error_msg)
                results["errors"].append(error_msg)

        # Post general comments as issue comments
        for comment in general_comments:
            try:
                await self._post_issue_comment(repository, pr_number, comment)
                results["posted_comments"] += 1
                results["general_comments"] += 1
            except Exception as e:
                error_msg = f"Failed to post general comment: {e}"
                logger.error(error_msg)
                results["errors"].append(error_msg)

        logger.info(f"Posted {results['posted_comments']} comments successfully")
        return results

    async def _get_pr_info(self, repository: str, pr_number: int) -> Dict[str, Any]:
        """Get PR information from GitHub API"""
        url = f"https://api.github.com/repos/{repository}/pulls/{pr_number}"

        # Get appropriate token for the repository
        token = await self._get_token_for_repository(repository)

        # Set headers with dynamic token
        headers = {}
        if token:
            headers["Authorization"] = f"token {token}"

        response = await self._make_request("GET", url, headers=headers)
        return response.json()

    async def _post_review_with_comments(
        self,
        repository: str,
        pr_number: int,
        comments: List[ReviewComment],
        commit_sha: str,
    ) -> Dict[str, Any]:
        """
        Post a PR review with inline comments.

        Args:
            repository: Repository name
            pr_number: PR number
            comments: Inline comments to post
            commit_sha: Commit SHA to review

        Returns:
            Review creation response
        """
        url = f"https://api.github.com/repos/{repository}/pulls/{pr_number}/reviews"

        # Convert ReviewComment objects to GitHub API format
        github_comments = []
        for comment in comments:
            github_comment = {
                "path": comment.file_path,
                "line": comment.line,
                "body": comment.body,
            }
            github_comments.append(github_comment)

        # Determine review event based on comment severity
        has_critical = any(
            c.severity and c.severity.value == "critical" for c in comments
        )
        has_high = any(c.severity and c.severity.value == "high" for c in comments)

        if has_critical:
            event = "REQUEST_CHANGES"
            review_body = "🚨 **Critical security issues found** - Please address these issues before merging."
        elif has_high:
            event = "REQUEST_CHANGES"
            review_body = "⚠️ **High severity security issues found** - Please review and address these issues."
        else:
            event = "COMMENT"
            review_body = (
                "🔍 **Security review completed** - Please review the findings below."
            )

        review_data = {
            "commit_id": commit_sha,
            "body": review_body,
            "event": event,
            "comments": github_comments,
        }

        # Get appropriate token for the repository
        token = await self._get_token_for_repository(repository)

        # Set headers with dynamic token
        headers = {}
        if token:
            headers["Authorization"] = f"token {token}"

        logger.debug(f"Posting review with {len(github_comments)} comments")
        response = await self._make_request("POST", url, review_data, headers)

        return response.json()

    async def _post_issue_comment(
        self, repository: str, pr_number: int, comment: ReviewComment
    ) -> Dict[str, Any]:
        """
        Post a general comment to the PR issue.

        Args:
            repository: Repository name
            pr_number: PR number
            comment: Comment to post

        Returns:
            Comment creation response
        """
        url = f"https://api.github.com/repos/{repository}/issues/{pr_number}/comments"

        comment_data = {"body": comment.body}

        # Get appropriate token for the repository
        token = await self._get_token_for_repository(repository)

        # Set headers with dynamic token
        headers = {}
        if token:
            headers["Authorization"] = f"token {token}"

        logger.debug(f"Posting issue comment to {repository}#{pr_number}")
        response = await self._make_request("POST", url, comment_data, headers)

        return response.json()

    async def update_review_comment(
        self, repository: str, comment_id: int, new_body: str
    ) -> Dict[str, Any]:
        """
        Update an existing review comment.

        Args:
            repository: Repository name
            comment_id: Comment ID to update
            new_body: New comment body

        Returns:
            Update response
        """
        url = f"https://api.github.com/repos/{repository}/pulls/comments/{comment_id}"

        update_data = {"body": new_body}

        logger.debug(f"Updating review comment {comment_id}")
        response = await self._make_request("PATCH", url, update_data)

        return response.json()

    async def delete_review_comment(self, repository: str, comment_id: int) -> bool:
        """
        Delete a review comment.

        Args:
            repository: Repository name
            comment_id: Comment ID to delete

        Returns:
            True if successful
        """
        url = f"https://api.github.com/repos/{repository}/pulls/comments/{comment_id}"

        logger.debug(f"Deleting review comment {comment_id}")
        await self._make_request("DELETE", url)

        return True

    async def get_existing_reviews(
        self, repository: str, pr_number: int
    ) -> List[Dict[str, Any]]:
        """
        Get existing reviews for a PR.

        Args:
            repository: Repository name
            pr_number: PR number

        Returns:
            List of existing reviews
        """
        url = f"https://api.github.com/repos/{repository}/pulls/{pr_number}/reviews"

        response = await self._make_request("GET", url)
        return response.json()

    async def dismiss_review(
        self,
        repository: str,
        pr_number: int,
        review_id: int,
        message: str = "Dismissing outdated security review",
    ) -> Dict[str, Any]:
        """
        Dismiss a PR review.

        Args:
            repository: Repository name
            pr_number: PR number
            review_id: Review ID to dismiss
            message: Dismissal message

        Returns:
            Dismissal response
        """
        url = f"https://api.github.com/repos/{repository}/pulls/{pr_number}/reviews/{review_id}/dismissals"

        dismiss_data = {"message": message}

        logger.debug(f"Dismissing review {review_id}")
        response = await self._make_request("PUT", url, dismiss_data)

        return response.json()

    async def check_permissions(self, repository: str) -> Dict[str, bool]:
        """
        Check what permissions the token has for the repository.

        Args:
            repository: Repository name

        Returns:
            Dictionary of permission checks
        """
        permissions = {
            "read": False,
            "write": False,
            "admin": False,
            "pull_requests": False,
        }

        try:
            # Try to get repository info
            url = f"https://api.github.com/repos/{repository}"
            response = await self._make_request("GET", url)
            repo_data = response.json()

            repo_permissions = repo_data.get("permissions", {})
            permissions.update(
                {
                    "read": repo_permissions.get("pull", False),
                    "write": repo_permissions.get("push", False),
                    "admin": repo_permissions.get("admin", False),
                    "pull_requests": repo_permissions.get("pull", False),
                }
            )

        except Exception as e:
            logger.warning(f"Could not check repository permissions: {e}")

        return permissions


# Convenience function for quick comment posting
async def post_security_comments(
    repository: str,
    pr_number: int,
    comments: List[ReviewComment],
    github_token: Optional[str] = None,
    commit_sha: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Convenience function to post security review comments.

    Args:
        repository: Repository name in format "owner/repo"
        pr_number: Pull request number
        comments: List of review comments to post
        github_token: Optional GitHub token for authentication
        commit_sha: Optional specific commit SHA to review

    Returns:
        Dictionary with posting results
    """
    service = GitHubCommentService(github_token)
    return await service.post_review_comments(
        repository, pr_number, comments, commit_sha
    )
