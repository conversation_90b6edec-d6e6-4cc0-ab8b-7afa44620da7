import asyncio
import time
import uuid
from typing import Any, Dict, List, Optional

import httpx
from fastapi import APIRouter, Body, Depends, Header, HTTPException, Request, status

from platyfend_ai.analyzers import <PERSON>tGrep<PERSON>nal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SemgrepAnalyzer
from platyfend_ai.config.settings import settings
from platyfend_ai.core.analysis_engine import analysis_engine
from platyfend_ai.database.mongodb_service import MongoDBService
from platyfend_ai.dependencies import (
    AnalysisServices,
    check_service_health,
    get_analysis_services,
)
from platyfend_ai.models.github import GitHubPRWebhookData
from platyfend_ai.services import (
    CommentGeneration<PERSON>rror,
    Diff<PERSON>etcher,
    SecurityCommentGenerator,
)
from platyfend_ai.services.cache_service import analysis_cache, cache_service
from platyfend_ai.utils.error_handling import (
    AnalysisError,
    GitHubAPIError,
    PlatyfendError,
    get_user_friendly_message,
    handle_exceptions,
)
from platyfend_ai.utils.logging_config import get_logger, set_request_context
from platyfend_ai.utils.security_service import SecurityService
from platyfend_ai.utils.webhook_security import validate_github_webhook

logger = get_logger(__name__)

github_routes = APIRouter(
    tags=["GitHub"], dependencies=[Depends(SecurityService.get_api_key)]
)


class IdempotencyService:
    """Service for handling webhook idempotency using X-GitHub-Delivery header."""

    def __init__(self):
        self.mongodb_service = MongoDBService()
        self.collection_name = "webhook_deliveries"
        self.ttl_seconds = 86400  # 24 hours TTL for delivery records

    async def is_delivery_processed(self, delivery_id: str) -> bool:
        """
        Check if a webhook delivery has already been processed.

        Args:
            delivery_id: GitHub delivery ID from X-GitHub-Delivery header

        Returns:
            True if already processed, False otherwise
        """
        try:
            collection = await self.mongodb_service.get_collection(self.collection_name)

            # Check if delivery ID exists
            existing = await collection.find_one({"delivery_id": delivery_id})

            if existing:
                logger.info(
                    f"Webhook delivery {delivery_id} already processed at {existing.get('processed_at')}"
                )
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking delivery idempotency: {e}")
            # On error, allow processing to continue (fail open)
            return False

    async def mark_delivery_processed(
        self, delivery_id: str, pr_data: GitHubPRWebhookData, status: str = "success"
    ) -> bool:
        """
        Mark a webhook delivery as processed.

        Args:
            delivery_id: GitHub delivery ID from X-GitHub-Delivery header
            pr_data: PR webhook data for context
            status: Processing status (success, error, skipped)

        Returns:
            True if successfully marked, False otherwise
        """
        try:
            collection = await self.mongodb_service.get_collection(self.collection_name)

            # Create TTL index if it doesn't exist
            await collection.create_index(
                "processed_at", expireAfterSeconds=self.ttl_seconds
            )

            delivery_record = {
                "delivery_id": delivery_id,
                "repository": pr_data.repository,
                "pr_number": pr_data.number,
                "pr_id": pr_data.id,
                "action": pr_data.action,
                "status": status,
                "processed_at": time.time(),
            }

            # Insert the delivery record
            await collection.insert_one(delivery_record)

            logger.info(
                f"Marked webhook delivery {delivery_id} as processed with status: {status}"
            )
            return True

        except Exception as e:
            logger.error(f"Error marking delivery as processed: {e}")
            return False


# Initialize idempotency service
idempotency_service = IdempotencyService()


async def mark_delivery_if_present(
    delivery_id: Optional[str], pr_data: GitHubPRWebhookData, status: str
) -> None:
    """Helper function to mark delivery as processed if delivery_id is present."""
    if delivery_id:
        try:
            await idempotency_service.mark_delivery_processed(
                delivery_id, pr_data, status
            )
        except Exception as mark_error:
            logger.error(
                f"Failed to mark delivery {delivery_id} as {status}: {mark_error}"
            )


async def validate_pr_for_analysis(
    pr_data: GitHubPRWebhookData, request_id: str
) -> Optional[Dict[str, Any]]:
    """
    Validate if PR should be analyzed based on various criteria.

    Args:
        pr_data: GitHub PR webhook data
        request_id: Request ID for logging

    Returns:
        None if PR should be analyzed, or dict with skip reason if it should be skipped
    """
    # Skip analysis if the webhook was triggered by the bot itself to prevent loops
    bot_usernames = ["platyfend-bot", "platyfend[bot]", "platyfend-test[bot]"]
    if pr_data.sender in bot_usernames:
        logger.info(
            f"[{request_id}] Skipping analysis - webhook triggered by bot user: {pr_data.sender}"
        )
        return {
            "status": "skipped",
            "reason": f"Bot-triggered event from {pr_data.sender}",
            "pr_number": pr_data.number,
            "repository": pr_data.repository,
        }

    # Only process specific PR actions that should trigger analysis
    allowed_actions = getattr(
        settings, "allowed_pr_actions", ["opened", "synchronize", "reopened"]
    )
    if pr_data.action not in allowed_actions:
        logger.info(
            f"[{request_id}] Skipping analysis - action '{pr_data.action}' not in allowed actions: {allowed_actions}"
        )
        return {
            "status": "skipped",
            "reason": f"Action '{pr_data.action}' not configured for analysis",
            "pr_number": pr_data.number,
            "repository": pr_data.repository,
        }

    # Skip analysis for draft PRs or closed PRs unless configured otherwise
    if pr_data.draft and not getattr(settings, "analyze_draft_prs", False):
        logger.info(f"[{request_id}] Skipping analysis for draft PR #{pr_data.number}")
        return {
            "status": "skipped",
            "reason": "Draft PR - analysis disabled",
            "pr_number": pr_data.number,
            "repository": pr_data.repository,
        }

    if pr_data.action == "closed":
        logger.info(f"[{request_id}] Skipping analysis for closed PR #{pr_data.number}")
        return {
            "status": "skipped",
            "reason": "PR closed",
            "pr_number": pr_data.number,
            "repository": pr_data.repository,
        }

    return None  # PR should be analyzed


async def check_analysis_cache(
    pr_data: GitHubPRWebhookData, request_id: str
) -> Optional[Dict[str, Any]]:
    """
    Check if PR was recently analyzed to prevent duplicate processing.

    Args:
        pr_data: GitHub PR webhook data
        request_id: Request ID for logging

    Returns:
        None if analysis should proceed, or dict with skip reason if recently analyzed
    """
    logger.info(
        f"[{request_id}] Cache check for PR #{pr_data.number} in {pr_data.repository}"
    )

    # Check if recently analyzed using Redis cache
    cache_result = await analysis_cache.check_recent_analysis(
        pr_data.repository, pr_data.number
    )

    if cache_result:
        logger.info(f"[{request_id}] Skipping analysis - {cache_result['reason']}")
        return cache_result

    # Mark this PR as being analyzed
    success = await analysis_cache.mark_analysis_started(
        pr_data.repository, pr_data.number
    )

    if success:
        logger.info(f"[{request_id}] Marked PR #{pr_data.number} as being analyzed")
    else:
        logger.warning(f"[{request_id}] Failed to mark PR #{pr_data.number} in cache")

    return None  # Analysis should proceed


async def fetch_pr_data(
    pr_data: GitHubPRWebhookData, request_id: str, diff_fetcher: DiffFetcher
) -> Dict[str, Any]:
    """
    Fetch PR diff and file information using injected service.

    Args:
        pr_data: GitHub PR webhook data
        request_id: Request ID for logging
        diff_fetcher: Injected DiffFetcher service

    Returns:
        Dict containing diff_content and files_info

    Raises:
        DiffFetchError: If fetching fails
    """
    logger.info(f"[{request_id}] Step 1/5: Fetching PR diff and file information")

    pr_data_complete = await diff_fetcher.fetch_pr_diff_and_files(
        pr_data.diff_url, pr_data.repository, pr_data.number
    )
    diff_content = pr_data_complete["diff_content"]
    files_info = pr_data_complete["files_info"]

    logger.info(
        f"[{request_id}] Step 1/5 completed: Fetched diff ({len(diff_content)} chars) and {files_info['total_files']} files"
    )

    return {"diff_content": diff_content, "files_info": files_info}


async def initialize_analyzers(request_id: str, analysis_engine, config) -> List[str]:
    """
    Initialize and register security analyzers using injected services.

    Args:
        request_id: Request ID for logging
        analysis_engine: Injected analysis engine
        config: Service configuration

    Returns:
        List of registered analyzer names
    """
    logger.info(f"[{request_id}] Step 2/5: Initializing security analyzers")

    # Register analyzers based on configuration
    if config.enable_semgrep:
        semgrep_analyzer = SemgrepAnalyzer()
        analysis_engine.register_analyzer(semgrep_analyzer)

    if config.enable_ast_grep:
        ast_grep_analyzer = AstGrepAnalyzer()
        analysis_engine.register_analyzer(ast_grep_analyzer)

    if config.enable_linters:
        linter_analyzer = LinterAnalyzer()
        analysis_engine.register_analyzer(linter_analyzer)

    registered_analyzers = analysis_engine.get_registered_analyzers()
    logger.info(
        f"[{request_id}] Step 2/5 completed: Registered {len(registered_analyzers)} analyzers: {registered_analyzers}"
    )

    return registered_analyzers


async def run_security_analysis(
    pr_data: GitHubPRWebhookData,
    diff_content: str,
    files_info: Dict[str, Any],
    request_id: str,
    analysis_engine,
) -> Dict[str, Any]:
    """
    Run security analysis on PR data.

    Args:
        pr_data: GitHub PR webhook data
        diff_content: PR diff content
        files_info: File information
        request_id: Request ID for logging

    Returns:
        Analysis report or error dict
    """
    logger.info(f"[{request_id}] Step 3/5: Running security analysis")

    try:
        analysis_report = await analysis_engine.analyze_pr(
            diff_content=diff_content,
            files_info=files_info,
            pr_id=pr_data.id,
            pr_number=pr_data.number,
            repository=pr_data.repository,
        )

        logger.info(
            f"[{request_id}] Step 3/5 completed: Analysis completed: {analysis_report.total_findings} findings, "
            f"{analysis_report.critical_findings} critical, "
            f"{analysis_report.high_findings} high severity"
        )

        return {"analysis_report": analysis_report}

    except AnalysisError as e:
        logger.error(f"[{request_id}] Analysis failed: {e}")
        return {
            "status": "error",
            "error": "Security analysis failed",
            "details": str(e),
            "pr_number": pr_data.number,
            "repository": pr_data.repository,
        }


async def generate_review_comments(
    analysis_report,
    pr_data: GitHubPRWebhookData,
    request_id: str,
    comment_generator: SecurityCommentGenerator,
) -> List[Any]:
    """
    Generate review comments based on analysis results.

    Args:
        analysis_report: Analysis report from security analysis
        pr_data: GitHub PR webhook data
        request_id: Request ID for logging

    Returns:
        List of review comments
    """
    logger.info(f"[{request_id}] Step 4/5: Generating security review comments")

    if analysis_report.total_findings > 0:
        try:
            # Collect all findings from analysis results
            all_findings = []
            for result in analysis_report.analysis_results:
                all_findings.extend(result.findings)

            # Generate review comments using injected service
            review_comments = await comment_generator.generate_review_comments(
                all_findings,
                pr_context={
                    "title": pr_data.title,
                    "repository": pr_data.repository,
                    "author": pr_data.author,
                    "number": pr_data.number,
                },
            )

            logger.info(
                f"[{request_id}] Step 4/5 completed: Generated {len(review_comments)} review comments for {len(all_findings)} findings"
            )
            return review_comments

        except CommentGenerationError as e:
            logger.error(f"[{request_id}] Comment generation failed: {e}")
            return []  # Continue with empty comments
    else:
        try:
            # Generate a positive security message when no issues are found using injected service
            review_comments = await comment_generator.generate_no_findings_message(
                pr_context={
                    "title": pr_data.title,
                    "repository": pr_data.repository,
                    "author": pr_data.author,
                    "number": pr_data.number,
                },
            )

            logger.info(
                f"[{request_id}] Step 4/5 completed: Generated positive security message for clean PR"
            )
            return review_comments

        except CommentGenerationError as e:
            logger.error(f"[{request_id}] Failed to generate no-findings message: {e}")
            return []


async def send_comments_to_frontend(pr_data, comments, analysis_report):
    """
    Send security review comments to Next.js frontend instead of GitHub.

    Args:
        pr_data: GitHub PR webhook data
        comments: List of review comments
        analysis_report: Analysis results

    Returns:
        Dict with results of sending comments
    """
    frontend_url = getattr(settings, "frontend_url", "http://localhost:3001")
    endpoint = f"{frontend_url}/api/security-review"

    # Prepare payload for frontend
    payload = {
        "pr_info": {
            "id": pr_data.id,
            "number": pr_data.number,
            "title": pr_data.title,
            "repository": pr_data.repository,
            "author": pr_data.author,
            "url": pr_data.url,
            "base_branch": pr_data.base_branch,
            "head_branch": pr_data.head_branch,
            "action": pr_data.action,
        },
        "analysis_summary": {
            "total_findings": analysis_report.total_findings,
            "critical_findings": analysis_report.critical_findings,
            "high_findings": analysis_report.high_findings,
            "success": analysis_report.success,
            "analyzers_run": len(analysis_report.analysis_results),
        },
        "comments": [
            {
                "body": comment.body,
                "file_path": comment.file_path or "",
                "line": comment.line or 0,
                "severity": comment.severity.value if comment.severity else "",
                "comment_type": comment.comment_type,
                "finding_ids": comment.finding_ids,
            }
            for comment in comments
        ],
        "timestamp": time.time(),
    }

    try:
        # Get API key for frontend authentication from settings
        api_key = getattr(settings, "api_key", None)

        headers = {"Content-Type": "application/json"}
        if api_key:
            headers["x-api-key"] = api_key

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(endpoint, json=payload, headers=headers)

            if response.status_code == 200:
                logger.info(f"Successfully sent {len(comments)} comments to frontend")
                return {
                    "sent_comments": len(comments),
                    "errors": [],
                    "frontend_response": response.json() if response.content else None,
                }
            else:
                error_msg = (
                    f"Frontend returned status {response.status_code}: {response.text}"
                )
                logger.error(error_msg)
                return {"sent_comments": 0, "errors": [error_msg]}

    except httpx.RequestError as e:
        error_msg = f"Failed to connect to frontend: {e}"
        logger.error(error_msg)
        return {"sent_comments": 0, "errors": [error_msg]}
    except Exception as e:
        error_msg = f"Unexpected error sending to frontend: {e}"
        logger.error(error_msg)
        return {"sent_comments": 0, "errors": [error_msg]}


@github_routes.post("/pr_open", response_model=Dict[str, Any])
async def github_pr_open(
    request: Request,
    pr_data: GitHubPRWebhookData = Body(..., description="GitHub PR webhook data"),
    x_github_delivery: Optional[str] = Header(
        None,
        alias="X-GitHub-Delivery",
        description="GitHub webhook delivery ID for idempotency",
    ),
    webhook_validated: bool = Depends(validate_github_webhook),
    analysis_services=Depends(get_analysis_services),
) -> Dict[str, Any]:
    """
    Handle GitHub Pull Request webhook events with security validation and idempotency support.

    This endpoint receives GitHub PR webhook data when a PR is opened, updated, or closed.
    It validates the webhook signature using HMAC-SHA256, processes the PR data, and triggers
    the appropriate analysis workflows. Uses X-GitHub-Delivery header for idempotency to
    prevent duplicate processing.

    Security Features:
    - HMAC-SHA256 signature validation using X-Hub-Signature-256 header
    - Idempotency protection using X-GitHub-Delivery header
    - API key authentication via SecurityService dependency

    Args:
        request: FastAPI Request object (used for signature validation)
        pr_data: GitHub PR webhook data containing all relevant PR information
        x_github_delivery: GitHub delivery ID for idempotency (from X-GitHub-Delivery header)
        webhook_validated: Dependency that validates webhook signature (automatically injected)

    Returns:
        Dict containing the processing status and any relevant information

    Raises:
        HTTPException: If processing fails, signature validation fails, or other errors occur
    """
    try:
        # Generate unique request ID for tracking
        request_id = str(uuid.uuid4())[:8]

        # Set structured logging context
        set_request_context(
            request_id=request_id, operation="pr_webhook", user_id=pr_data.author
        )

        logger.info(
            "Received PR webhook",
            repository=pr_data.repository,
            pr_number=pr_data.number,
            pr_title=pr_data.title,
            action=pr_data.action,
            state=pr_data.state,
            author=pr_data.author,
            sender=pr_data.sender,
            delivery_id=x_github_delivery,
        )

        # Log delivery ID for tracking
        if x_github_delivery:
            logger.info("GitHub delivery ID received", delivery_id=x_github_delivery)
        else:
            logger.warning(
                "No X-GitHub-Delivery header found - idempotency not guaranteed"
            )

        # Check idempotency using GitHub delivery ID
        if x_github_delivery:
            is_already_processed = await idempotency_service.is_delivery_processed(
                x_github_delivery
            )
            if is_already_processed:
                logger.info(
                    f"[{request_id}] Webhook delivery {x_github_delivery} already processed - returning cached response"
                )
                return {
                    "status": "skipped",
                    "reason": f"Webhook delivery {x_github_delivery} already processed",
                    "pr_number": pr_data.number,
                    "repository": pr_data.repository,
                    "delivery_id": x_github_delivery,
                }

        # Log key PR metrics
        logger.info(
            f"[{request_id}] PR stats - Commits: {pr_data.commits}, "
            f"Files changed: {pr_data.changed_files}, "
            f"Additions: {pr_data.additions}, "
            f"Deletions: {pr_data.deletions}"
        )

        # Initialize the PR processing pipeline
        response_data = await process_pr_security_analysis(
            pr_data, analysis_services, request_id, x_github_delivery
        )

        return response_data

    except Exception as e:
        logger.error(f"Error processing PR webhook: {str(e)}", exc_info=True)

        # Mark delivery as failed if we have a delivery ID
        await mark_delivery_if_present(x_github_delivery, pr_data, "error")

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process PR webhook: {str(e)}",
        )


@handle_exceptions("github_routes", "process_pr_security_analysis")
async def process_pr_security_analysis(
    pr_data: GitHubPRWebhookData,
    analysis_services: AnalysisServices,
    request_id: str = "",
    delivery_id: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Process PR security analysis pipeline using modular helper functions.

    Args:
        pr_data: GitHub PR webhook data
        request_id: Unique request identifier for tracking
        delivery_id: Optional delivery ID for idempotency tracking

    Returns:
        Processing results
    """
    try:
        # Generate request ID if not provided
        if not request_id:
            request_id = str(uuid.uuid4())[:8]

        logger.info(
            f"[{request_id}] Starting security analysis pipeline for PR #{pr_data.number} in {pr_data.repository} (action: {pr_data.action}, sender: {pr_data.sender})"
        )

        # Validate if PR should be analyzed
        validation_result = await validate_pr_for_analysis(pr_data, request_id)
        if validation_result:
            await mark_delivery_if_present(delivery_id, pr_data, "skipped")
            return validation_result

        # Check analysis cache to prevent duplicate processing
        cache_result = await check_analysis_cache(pr_data, request_id)
        if cache_result:
            await mark_delivery_if_present(delivery_id, pr_data, "skipped")
            return cache_result

        # Fetch PR data (Step 1)
        try:
            pr_fetch_result = await fetch_pr_data(
                pr_data, request_id, analysis_services.diff_fetcher
            )
            diff_content = pr_fetch_result["diff_content"]
            files_info = pr_fetch_result["files_info"]
        except GitHubAPIError as e:
            logger.error(f"[{request_id}] Failed to fetch PR data: {e}")
            await mark_delivery_if_present(delivery_id, pr_data, "error")
            return {
                "status": "error",
                "error": "Failed to fetch PR diff",
                "details": str(e),
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        # Initialize analyzers (Step 2)
        registered_analyzers = await initialize_analyzers(
            request_id, analysis_services.analysis_engine, analysis_services.config
        )

        if not registered_analyzers:
            logger.warning("No analyzers available for analysis")
            return {
                "status": "error",
                "error": "No analyzers available",
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        # Run security analysis (Step 3)
        analysis_result = await run_security_analysis(
            pr_data,
            diff_content,
            files_info,
            request_id,
            analysis_services.analysis_engine,
        )
        if "status" in analysis_result and analysis_result["status"] == "error":
            await mark_delivery_if_present(delivery_id, pr_data, "error")
            return analysis_result

        analysis_report = analysis_result["analysis_report"]

        # Generate review comments (Step 4)
        review_comments = await generate_review_comments(
            analysis_report, pr_data, request_id, analysis_services.comment_generator
        )

        # Send comments to frontend (Step 5)
        logger.info(f"[{request_id}] Step 5/5: Sending comments to Next.js frontend")

        comment_results = {"sent_comments": 0, "errors": []}

        if review_comments:
            try:
                comment_results = await send_comments_to_frontend(
                    pr_data=pr_data,
                    comments=review_comments,
                    analysis_report=analysis_report,
                )
                logger.info(
                    f"[{request_id}] Step 5/5 completed: Sent {comment_results['sent_comments']} comments to frontend"
                )

            except Exception as e:
                logger.error(f"[{request_id}] Failed to send comments to frontend: {e}")
                comment_results["errors"].append(str(e))
        else:
            logger.info(
                f"[{request_id}] Step 5/5 completed: No review comments generated - skipped comment sending"
            )

        # Prepare response
        response_data = {
            "status": "success",
            "message": f"Security analysis completed for PR #{pr_data.number}",
            "pr_info": {
                "id": pr_data.id,
                "number": pr_data.number,
                "title": pr_data.title,
                "repository": pr_data.repository,
                "action": pr_data.action,
                "author": pr_data.author,
                "base_branch": pr_data.base_branch,
                "head_branch": pr_data.head_branch,
                "draft": pr_data.draft,
                "mergeable": pr_data.mergeable,
                "stats": {
                    "commits": pr_data.commits,
                    "changed_files": pr_data.changed_files,
                    "additions": pr_data.additions,
                    "deletions": pr_data.deletions,
                },
            },
            "analysis_results": {
                "total_findings": analysis_report.total_findings,
                "critical_findings": analysis_report.critical_findings,
                "high_findings": analysis_report.high_findings,
                "analyzers_run": len(analysis_report.analysis_results),
                "success": analysis_report.success,
                "errors": analysis_report.errors,
            },
            "comment_results": comment_results,
            "processing_time": (
                (
                    analysis_report.completed_at - analysis_report.started_at
                ).total_seconds()
                if analysis_report.completed_at
                else None
            ),
        }

        logger.info(
            f"[{request_id}] Successfully completed all 5 steps of security analysis pipeline for PR #{pr_data.number}"
        )

        # Mark delivery as successfully processed if we have a delivery ID
        await mark_delivery_if_present(delivery_id, pr_data, "success")

        return response_data

    except PlatyfendError as e:
        logger.error(f"Platyfend error in PR processing: {e}")

        # Mark delivery as failed if we have a delivery ID
        await mark_delivery_if_present(delivery_id, pr_data, "error")

        return {
            "status": "error",
            "error": get_user_friendly_message(e),
            "error_id": e.context.error_id if hasattr(e, "context") else None,
            "pr_number": pr_data.number,
            "repository": pr_data.repository,
        }
    except Exception as e:
        logger.error(f"Unexpected error in PR processing: {e}", exc_info=True)

        # Mark delivery as failed if we have a delivery ID
        await mark_delivery_if_present(delivery_id, pr_data, "error")

        return {
            "status": "error",
            "error": "An unexpected error occurred during processing",
            "details": str(e),
            "pr_number": pr_data.number,
            "repository": pr_data.repository,
        }


@github_routes.get("/health", response_model=Dict[str, Any])
async def health_check() -> Dict[str, Any]:
    """
    Health check endpoint that verifies all services are properly configured.

    Returns:
        Dict containing health status of all services
    """
    try:
        health_status = await check_service_health()

        # Determine overall health
        unhealthy_services = [
            service
            for service, status in health_status.items()
            if status in ["unhealthy", "not_configured", "error"]
        ]

        overall_status = "healthy" if not unhealthy_services else "degraded"

        return {
            "status": overall_status,
            "services": health_status,
            "unhealthy_services": unhealthy_services,
            "timestamp": time.time(),
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {"status": "error", "error": str(e), "timestamp": time.time()}
